# DEA效率前沿面分析程序

## 项目简介
本项目使用数据包络分析（DEA）方法分析31省份2015-2024年移动数据流量（mobiledatatraffic）与GPU的效率关系，计算最优前沿面并进行可视化。

## 文件说明

### 主要程序文件
1. **dea_analysis.py** - 完整版DEA分析程序
   - 使用scipy.optimize进行线性规划求解
   - 实现标准的DEA-VRS模型
   - 提供详细的效率分析和报告

2. **simple_dea.py** - 简化版DEA分析程序
   - 适用于基础Python环境
   - 使用简化的帕累托前沿概念
   - 不依赖复杂的优化库

3. **requirements.txt** - Python依赖包列表

### 数据文件
- **数据.xlsx** - 31省份面板数据，包含mobiledatatraffic和gpu两个变量

## 使用方法

### 方法一：运行完整版程序（推荐）
```bash
# 1. 安装依赖包
pip install -r requirements.txt

# 2. 运行完整版分析
python dea_analysis.py
```

### 方法二：运行简化版程序
```bash
# 如果无法安装scipy等包，可以运行简化版
python simple_dea.py
```

## 程序功能

### 数据处理
- 自动读取Excel文件
- 智能识别mobiledatatraffic和gpu列
- 处理缺失值和异常数据
- 数据验证和预处理

### DEA分析
- **投入指标**: 移动数据流量 (mobiledatatraffic)
- **产出指标**: GPU
- **模型类型**: VRS (可变规模报酬) DEA模型
- **分析方向**: 产出导向

### 效率前沿面计算
- 识别效率前沿面上的省份
- 计算每个省份的效率得分
- 生成连续的效率前沿面曲线
- 分析投入产出的最优组合

### 可视化输出
- 散点图显示所有省份的投入产出情况
- 红色点标记效率前沿面上的省份
- 红色曲线显示理论最优前沿面
- 包含统计信息和图例说明

### 分析报告
- 数据概况统计
- 效率得分分析
- 前沿面省份识别
- 效率分组分析
- 改进建议

## 输出文件
- **dea_frontier.png** - 完整版程序生成的前沿面图
- **simple_dea_frontier.png** - 简化版程序生成的前沿面图

## 技术特点

### DEA模型原理
数据包络分析（DEA）是一种非参数的效率评价方法，通过线性规划技术构建生产前沿面，评价决策单元的相对效率。

### 模型公式
对于决策单元i，DEA-VRS模型为：
```
max θ
s.t. Σλⱼxⱼ ≤ xᵢ
     Σλⱼyⱼ ≥ θyᵢ
     Σλⱼ = 1
     λⱼ ≥ 0
```

其中：
- θ: 效率得分
- xⱼ, yⱼ: 第j个省份的投入和产出
- λⱼ: 权重系数

### 前沿面构建
1. 识别效率得分为1的省份（前沿面上的点）
2. 对不同投入水平求解最大可能产出
3. 连接这些点形成连续的前沿面曲线

## 结果解释

### 效率得分
- **1.0**: 位于效率前沿面上，相对效率最高
- **<1.0**: 存在改进空间，数值越小效率越低

### 前沿面意义
- 前沿面代表给定投入下的最大可能产出
- 前沿面上的省份为其他省份提供最佳实践参考
- 前沿面下方的省份可以通过学习前沿面省份的经验来提高效率

## 注意事项
1. 确保数据文件名为"数据.xlsx"且位于程序同一目录
2. 数据文件应包含mobiledatatraffic和gpu两列
3. 程序会自动处理中文列名和模糊匹配
4. 建议使用完整版程序以获得更准确的结果

## 故障排除
- 如果出现编码错误，请确保Excel文件为UTF-8编码
- 如果无法安装scipy，请使用简化版程序
- 如果图表中文显示异常，请安装中文字体

## 联系方式
如有问题请检查数据格式或程序依赖包安装情况。

"""
数据包络分析（DEA）- 移动数据流量与GPU效率前沿面分析
作者：数据分析程序
功能：计算31省份mobiledatatraffic投入与GPU产出的效率前沿面并可视化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import linprog
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class DEAAnalysis:
    """数据包络分析类"""
    
    def __init__(self, data_file):
        """
        初始化DEA分析
        
        参数:
        data_file: Excel数据文件路径
        """
        self.data_file = data_file
        self.data = None
        self.inputs = None
        self.outputs = None
        self.efficiency_scores = None
        self.frontier_points = None
        
    def load_data(self):
        """加载和预处理数据"""
        try:
            # 读取Excel文件
            self.data = pd.read_excel(self.data_file)
            print(f"数据加载成功，形状: {self.data.shape}")
            print(f"列名: {self.data.columns.tolist()}")
            print("\n前5行数据:")
            print(self.data.head())
            
            # 检查是否包含必要的列
            required_cols = ['mobiledatatraffic', 'gpu']
            missing_cols = [col for col in required_cols if col not in self.data.columns]
            
            if missing_cols:
                print(f"警告：缺少必要列 {missing_cols}")
                # 尝试模糊匹配列名
                self._match_columns()
            else:
                self.inputs = self.data['mobiledatatraffic'].values
                self.outputs = self.data['gpu'].values
                
            # 移除缺失值
            valid_mask = ~(np.isnan(self.inputs) | np.isnan(self.outputs))
            self.inputs = self.inputs[valid_mask]
            self.outputs = self.outputs[valid_mask]
            
            print(f"\n有效数据点数量: {len(self.inputs)}")
            print(f"投入(mobiledatatraffic)范围: {self.inputs.min():.2f} - {self.inputs.max():.2f}")
            print(f"产出(gpu)范围: {self.outputs.min():.2f} - {self.outputs.max():.2f}")
            
        except Exception as e:
            print(f"数据加载错误: {e}")
            return False
        return True
    
    def _match_columns(self):
        """模糊匹配列名"""
        cols = self.data.columns.tolist()
        
        # 查找包含mobile、data、traffic的列
        mobile_cols = [col for col in cols if any(keyword in col.lower() 
                      for keyword in ['mobile', 'data', 'traffic', '移动', '数据', '流量'])]
        
        # 查找包含gpu的列
        gpu_cols = [col for col in cols if 'gpu' in col.lower() or 'GPU' in col]
        
        if mobile_cols and gpu_cols:
            print(f"找到可能的投入列: {mobile_cols[0]}")
            print(f"找到可能的产出列: {gpu_cols[0]}")
            self.inputs = self.data[mobile_cols[0]].values
            self.outputs = self.data[gpu_cols[0]].values
        else:
            print("无法自动匹配列名，请检查数据文件")
            print(f"可用列名: {cols}")
    
    def calculate_efficiency_scores(self):
        """计算每个决策单元的效率得分"""
        n = len(self.inputs)
        self.efficiency_scores = np.zeros(n)
        
        for i in range(n):
            # 对每个决策单元求解DEA模型
            efficiency = self._solve_dea_model(i)
            self.efficiency_scores[i] = efficiency
            
        print(f"\n效率得分计算完成")
        print(f"平均效率: {self.efficiency_scores.mean():.4f}")
        print(f"效率范围: {self.efficiency_scores.min():.4f} - {self.efficiency_scores.max():.4f}")
        print(f"效率为1的单元数量: {np.sum(self.efficiency_scores >= 0.999)}")
        
    def _solve_dea_model(self, dmu_index):
        """
        求解单个决策单元的DEA模型（输出导向）
        
        参数:
        dmu_index: 决策单元索引
        
        返回:
        效率得分
        """
        n = len(self.inputs)
        
        # 目标函数：最大化效率 = 最小化 -theta
        c = np.zeros(n + 1)
        c[0] = -1  # theta系数
        
        # 约束条件矩阵
        # lambda * inputs <= inputs[dmu_index]
        # lambda * outputs >= theta * outputs[dmu_index]
        
        A_ub = []
        b_ub = []
        
        # 投入约束: sum(lambda_j * input_j) <= input_i
        A_ub.append(np.concatenate([[0], self.inputs]))
        b_ub.append(self.inputs[dmu_index])
        
        # 产出约束: sum(lambda_j * output_j) >= theta * output_i
        # 转换为: -sum(lambda_j * output_j) + theta * output_i <= 0
        A_ub.append(np.concatenate([[-self.outputs[dmu_index]], -self.outputs]))
        b_ub.append(0)
        
        # 等式约束: sum(lambda) = 1 (VRS假设)
        A_eq = np.concatenate([[0], np.ones(n)]).reshape(1, -1)
        b_eq = [1]
        
        # 变量边界: theta无约束, lambda >= 0
        bounds = [(None, None)] + [(0, None)] * n
        
        try:
            result = linprog(c, A_ub=A_ub, b_ub=b_ub, A_eq=A_eq, b_eq=b_eq, 
                           bounds=bounds, method='highs')
            if result.success:
                return result.x[0]  # theta值
            else:
                return 1.0
        except:
            return 1.0
    
    def calculate_frontier(self, num_points=100):
        """
        计算效率前沿面
        
        参数:
        num_points: 前沿面上的点数
        """
        # 找到效率为1的决策单元（前沿面上的点）
        efficient_mask = self.efficiency_scores >= 0.999
        efficient_inputs = self.inputs[efficient_mask]
        efficient_outputs = self.outputs[efficient_mask]
        
        print(f"\n前沿面上的有效单元数量: {np.sum(efficient_mask)}")
        
        # 生成投入水平范围
        input_min = self.inputs.min()
        input_max = self.inputs.max()
        input_range = np.linspace(input_min, input_max, num_points)
        
        frontier_outputs = []
        
        for input_level in input_range:
            # 对每个投入水平，计算最大可能产出
            max_output = self._calculate_max_output(input_level, efficient_inputs, efficient_outputs)
            frontier_outputs.append(max_output)
        
        self.frontier_points = {
            'inputs': input_range,
            'outputs': np.array(frontier_outputs)
        }
        
        print(f"前沿面计算完成，包含 {len(input_range)} 个点")
    
    def _calculate_max_output(self, input_level, efficient_inputs, efficient_outputs):
        """
        计算给定投入水平下的最大产出
        
        参数:
        input_level: 投入水平
        efficient_inputs: 有效单元的投入
        efficient_outputs: 有效单元的产出
        
        返回:
        最大产出水平
        """
        n = len(efficient_inputs)
        
        if n == 0:
            return 0
        
        # 线性规划求解最大产出
        # 目标函数：最大化 sum(lambda_j * output_j)
        c = -efficient_outputs  # 负号因为linprog求最小值
        
        # 约束条件：sum(lambda_j * input_j) <= input_level
        A_ub = efficient_inputs.reshape(1, -1)
        b_ub = [input_level]
        
        # 等式约束：sum(lambda_j) = 1
        A_eq = np.ones(n).reshape(1, -1)
        b_eq = [1]
        
        # 变量边界：lambda_j >= 0
        bounds = [(0, None)] * n
        
        try:
            result = linprog(c, A_ub=A_ub, b_ub=b_ub, A_eq=A_eq, b_eq=b_eq, 
                           bounds=bounds, method='highs')
            if result.success:
                return -result.fun  # 返回最大值
            else:
                # 如果求解失败，使用简单的线性插值
                return np.interp(input_level, efficient_inputs, efficient_outputs)
        except:
            return np.interp(input_level, efficient_inputs, efficient_outputs)
    
    def plot_frontier(self, save_path='dea_frontier.png', figsize=(12, 8)):
        """
        绘制效率前沿面图
        
        参数:
        save_path: 图片保存路径
        figsize: 图片尺寸
        """
        plt.figure(figsize=figsize)
        
        # 绘制所有数据点
        plt.scatter(self.inputs, self.outputs, alpha=0.6, s=50, 
                   c='lightblue', label='所有省份', edgecolors='black', linewidth=0.5)
        
        # 标记效率为1的点
        efficient_mask = self.efficiency_scores >= 0.999
        plt.scatter(self.inputs[efficient_mask], self.outputs[efficient_mask], 
                   alpha=0.8, s=100, c='red', label='效率前沿面上的省份', 
                   edgecolors='darkred', linewidth=1)
        
        # 绘制前沿面
        if self.frontier_points is not None:
            plt.plot(self.frontier_points['inputs'], self.frontier_points['outputs'], 
                    'r-', linewidth=2, label='效率前沿面', alpha=0.8)
        
        plt.xlabel('移动数据流量 (Mobile Data Traffic)', fontsize=12)
        plt.ylabel('GPU', fontsize=12)
        plt.title('31省份移动数据流量与GPU的DEA效率前沿面分析', fontsize=14, fontweight='bold')
        plt.legend(fontsize=10)
        plt.grid(True, alpha=0.3)
        
        # 添加统计信息
        stats_text = f'总样本数: {len(self.inputs)}\n'
        stats_text += f'前沿面上省份数: {np.sum(efficient_mask)}\n'
        stats_text += f'平均效率: {self.efficiency_scores.mean():.3f}'
        
        plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
                fontsize=10, verticalalignment='top', 
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"\n图表已保存至: {save_path}")
    
    def generate_report(self):
        """生成分析报告"""
        print("\n" + "="*60)
        print("DEA效率前沿面分析报告")
        print("="*60)
        
        print(f"\n1. 数据概况:")
        print(f"   - 样本数量: {len(self.inputs)} 个省份")
        print(f"   - 投入指标: 移动数据流量")
        print(f"   - 产出指标: GPU")
        
        print(f"\n2. 效率分析:")
        print(f"   - 平均效率得分: {self.efficiency_scores.mean():.4f}")
        print(f"   - 效率得分标准差: {self.efficiency_scores.std():.4f}")
        print(f"   - 最高效率: {self.efficiency_scores.max():.4f}")
        print(f"   - 最低效率: {self.efficiency_scores.min():.4f}")
        
        efficient_count = np.sum(self.efficiency_scores >= 0.999)
        print(f"   - 效率前沿面上的省份数量: {efficient_count}")
        print(f"   - 效率前沿面省份占比: {efficient_count/len(self.inputs)*100:.1f}%")
        
        print(f"\n3. 投入产出分析:")
        print(f"   - 移动数据流量范围: {self.inputs.min():.2f} - {self.inputs.max():.2f}")
        print(f"   - GPU范围: {self.outputs.min():.2f} - {self.outputs.max():.2f}")
        
        # 效率分组分析
        high_eff = self.efficiency_scores >= 0.8
        medium_eff = (self.efficiency_scores >= 0.6) & (self.efficiency_scores < 0.8)
        low_eff = self.efficiency_scores < 0.6
        
        print(f"\n4. 效率分组:")
        print(f"   - 高效率省份 (≥0.8): {np.sum(high_eff)} 个 ({np.sum(high_eff)/len(self.inputs)*100:.1f}%)")
        print(f"   - 中等效率省份 (0.6-0.8): {np.sum(medium_eff)} 个 ({np.sum(medium_eff)/len(self.inputs)*100:.1f}%)")
        print(f"   - 低效率省份 (<0.6): {np.sum(low_eff)} 个 ({np.sum(low_eff)/len(self.inputs)*100:.1f}%)")


def main():
    """主函数"""
    print("开始DEA效率前沿面分析...")
    
    # 创建DEA分析实例
    dea = DEAAnalysis('数据.xlsx')
    
    # 加载数据
    if not dea.load_data():
        print("数据加载失败，程序退出")
        return
    
    # 计算效率得分
    print("\n正在计算效率得分...")
    dea.calculate_efficiency_scores()
    
    # 计算前沿面
    print("\n正在计算效率前沿面...")
    dea.calculate_frontier()
    
    # 绘制图表
    print("\n正在生成可视化图表...")
    dea.plot_frontier()
    
    # 生成报告
    dea.generate_report()
    
    print("\nDEA分析完成！")


if __name__ == "__main__":
    main()

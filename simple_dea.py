"""
简化版DEA分析 - 移动数据流量与GPU效率前沿面
适用于基础Python环境
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_and_check_data():
    """加载并检查数据"""
    try:
        # 读取Excel文件
        data = pd.read_excel('数据.xlsx')
        print(f"数据加载成功！")
        print(f"数据形状: {data.shape}")
        print(f"列名: {data.columns.tolist()}")
        print("\n前5行数据:")
        print(data.head())
        print("\n数据信息:")
        print(data.info())
        print("\n描述性统计:")
        print(data.describe())
        
        return data
    except Exception as e:
        print(f"数据加载错误: {e}")
        return None

def simple_frontier_analysis(data):
    """简化的前沿面分析"""
    # 尝试找到mobiledatatraffic和gpu列
    cols = data.columns.tolist()
    
    # 查找相关列
    mobile_col = None
    gpu_col = None
    
    for col in cols:
        col_lower = col.lower()
        if any(keyword in col_lower for keyword in ['mobile', 'data', 'traffic', '移动', '数据', '流量']):
            mobile_col = col
            break
    
    for col in cols:
        if 'gpu' in col.lower():
            gpu_col = col
            break
    
    if mobile_col is None or gpu_col is None:
        print("无法找到mobiledatatraffic和gpu列")
        print("请确认数据文件包含这些列")
        return None, None
    
    print(f"使用列: {mobile_col} (投入), {gpu_col} (产出)")
    
    # 提取数据
    inputs = data[mobile_col].values
    outputs = data[gpu_col].values
    
    # 移除缺失值
    valid_mask = ~(pd.isna(inputs) | pd.isna(outputs))
    inputs = inputs[valid_mask]
    outputs = outputs[valid_mask]
    
    print(f"有效数据点: {len(inputs)}")
    
    return inputs, outputs

def calculate_simple_efficiency(inputs, outputs):
    """计算简单的效率比率"""
    # 计算产出投入比
    efficiency_ratios = outputs / inputs
    
    # 标准化到0-1范围
    max_ratio = efficiency_ratios.max()
    normalized_efficiency = efficiency_ratios / max_ratio
    
    return normalized_efficiency

def find_frontier_points(inputs, outputs, efficiency_scores):
    """找到前沿面上的点"""
    # 使用效率得分和帕累托前沿概念
    n = len(inputs)
    is_efficient = np.zeros(n, dtype=bool)
    
    for i in range(n):
        # 检查是否被其他点支配
        dominated = False
        for j in range(n):
            if i != j:
                # 如果存在其他点在投入不增加的情况下产出更高
                if inputs[j] <= inputs[i] and outputs[j] >= outputs[i] and (inputs[j] < inputs[i] or outputs[j] > outputs[i]):
                    dominated = True
                    break
        is_efficient[i] = not dominated
    
    return is_efficient

def create_frontier_curve(inputs, outputs, is_efficient):
    """创建前沿面曲线"""
    # 获取前沿面上的点
    frontier_inputs = inputs[is_efficient]
    frontier_outputs = outputs[is_efficient]
    
    # 按投入排序
    sort_idx = np.argsort(frontier_inputs)
    frontier_inputs = frontier_inputs[sort_idx]
    frontier_outputs = frontier_outputs[sort_idx]
    
    # 生成平滑的前沿面
    input_range = np.linspace(inputs.min(), inputs.max(), 100)
    frontier_curve = np.interp(input_range, frontier_inputs, frontier_outputs)
    
    return input_range, frontier_curve

def plot_results(inputs, outputs, is_efficient, input_range, frontier_curve):
    """绘制结果图"""
    plt.figure(figsize=(12, 8))
    
    # 绘制所有数据点
    plt.scatter(inputs[~is_efficient], outputs[~is_efficient], 
               alpha=0.6, s=50, c='lightblue', label='非效率省份', 
               edgecolors='black', linewidth=0.5)
    
    # 绘制前沿面上的点
    plt.scatter(inputs[is_efficient], outputs[is_efficient], 
               alpha=0.8, s=100, c='red', label='效率前沿面上的省份', 
               edgecolors='darkred', linewidth=1)
    
    # 绘制前沿面曲线
    plt.plot(input_range, frontier_curve, 'r-', linewidth=2, 
            label='效率前沿面', alpha=0.8)
    
    plt.xlabel('移动数据流量 (Mobile Data Traffic)', fontsize=12)
    plt.ylabel('GPU', fontsize=12)
    plt.title('31省份移动数据流量与GPU的DEA效率前沿面分析', fontsize=14, fontweight='bold')
    plt.legend(fontsize=10)
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    efficient_count = np.sum(is_efficient)
    stats_text = f'总样本数: {len(inputs)}\n'
    stats_text += f'前沿面上省份数: {efficient_count}\n'
    stats_text += f'效率省份占比: {efficient_count/len(inputs)*100:.1f}%'
    
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
            fontsize=10, verticalalignment='top', 
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('simple_dea_frontier.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("图表已保存为: simple_dea_frontier.png")

def generate_simple_report(inputs, outputs, is_efficient):
    """生成简单报告"""
    print("\n" + "="*50)
    print("DEA效率前沿面分析报告")
    print("="*50)
    
    efficient_count = np.sum(is_efficient)
    
    print(f"\n数据概况:")
    print(f"- 总样本数: {len(inputs)} 个省份")
    print(f"- 投入范围: {inputs.min():.2f} - {inputs.max():.2f}")
    print(f"- 产出范围: {outputs.min():.2f} - {outputs.max():.2f}")
    
    print(f"\n效率分析:")
    print(f"- 效率前沿面上的省份: {efficient_count} 个")
    print(f"- 效率省份占比: {efficient_count/len(inputs)*100:.1f}%")
    print(f"- 需要改进的省份: {len(inputs) - efficient_count} 个")
    
    # 计算平均效率
    efficiency_ratios = outputs / inputs
    max_ratio = efficiency_ratios.max()
    avg_efficiency = (efficiency_ratios / max_ratio).mean()
    
    print(f"- 平均相对效率: {avg_efficiency:.3f}")

def main():
    """主函数"""
    print("开始简化版DEA分析...")
    
    # 加载数据
    data = load_and_check_data()
    if data is None:
        return
    
    # 提取投入产出数据
    inputs, outputs = simple_frontier_analysis(data)
    if inputs is None:
        return
    
    # 计算效率得分
    efficiency_scores = calculate_simple_efficiency(inputs, outputs)
    
    # 找到前沿面上的点
    is_efficient = find_frontier_points(inputs, outputs, efficiency_scores)
    
    # 创建前沿面曲线
    input_range, frontier_curve = create_frontier_curve(inputs, outputs, is_efficient)
    
    # 绘制结果
    plot_results(inputs, outputs, is_efficient, input_range, frontier_curve)
    
    # 生成报告
    generate_simple_report(inputs, outputs, is_efficient)
    
    print("\n简化版DEA分析完成！")

if __name__ == "__main__":
    main()

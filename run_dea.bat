@echo off
echo 开始DEA效率前沿面分析...
echo.

echo 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo Python未找到，尝试使用py命令...
    py --version
    if %errorlevel% neq 0 (
        echo 错误：未找到Python环境
        pause
        exit /b 1
    )
    set PYTHON_CMD=py
) else (
    set PYTHON_CMD=python
)

echo.
echo 尝试运行完整版DEA分析...
%PYTHON_CMD% dea_analysis.py
if %errorlevel% neq 0 (
    echo.
    echo 完整版运行失败，尝试简化版...
    %PYTHON_CMD% simple_dea.py
    if %errorlevel% neq 0 (
        echo.
        echo 简化版也运行失败，请检查：
        echo 1. Python环境是否正确安装
        echo 2. 数据.xlsx文件是否存在
        echo 3. 必要的Python包是否已安装
        echo.
        echo 尝试安装依赖包...
        %PYTHON_CMD% -m pip install pandas numpy matplotlib openpyxl
        echo.
        echo 再次尝试运行简化版...
        %PYTHON_CMD% simple_dea.py
    )
)

echo.
echo 分析完成！
pause

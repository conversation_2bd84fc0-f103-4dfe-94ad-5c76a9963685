"""
数据检查脚本 - 验证Excel文件内容
"""

def check_data():
    """检查数据文件"""
    try:
        import pandas as pd
        print("✓ pandas库可用")
    except ImportError:
        print("✗ pandas库未安装")
        print("请运行: pip install pandas openpyxl")
        return False
    
    try:
        # 读取Excel文件
        print("\n正在读取数据.xlsx...")
        data = pd.read_excel('数据.xlsx')
        print("✓ 文件读取成功")
        
        # 显示基本信息
        print(f"\n数据形状: {data.shape}")
        print(f"行数: {data.shape[0]}, 列数: {data.shape[1]}")
        
        print(f"\n列名:")
        for i, col in enumerate(data.columns):
            print(f"  {i+1}. {col}")
        
        print(f"\n前5行数据:")
        print(data.head())
        
        print(f"\n数据类型:")
        print(data.dtypes)
        
        print(f"\n缺失值统计:")
        print(data.isnull().sum())
        
        print(f"\n描述性统计:")
        print(data.describe())
        
        # 查找目标列
        cols = data.columns.tolist()
        mobile_cols = []
        gpu_cols = []
        
        for col in cols:
            col_lower = col.lower()
            if any(keyword in col_lower for keyword in ['mobile', 'data', 'traffic', '移动', '数据', '流量']):
                mobile_cols.append(col)
            if 'gpu' in col_lower:
                gpu_cols.append(col)
        
        print(f"\n目标列识别:")
        print(f"可能的移动数据流量列: {mobile_cols}")
        print(f"可能的GPU列: {gpu_cols}")
        
        if mobile_cols and gpu_cols:
            print("✓ 找到目标列，可以进行DEA分析")
            
            # 显示目标列的数据
            mobile_col = mobile_cols[0]
            gpu_col = gpu_cols[0]
            
            print(f"\n使用列进行分析:")
            print(f"投入指标: {mobile_col}")
            print(f"产出指标: {gpu_col}")
            
            # 检查数据质量
            mobile_data = data[mobile_col]
            gpu_data = data[gpu_col]
            
            print(f"\n数据质量检查:")
            print(f"{mobile_col} - 缺失值: {mobile_data.isnull().sum()}, 范围: {mobile_data.min():.2f} - {mobile_data.max():.2f}")
            print(f"{gpu_col} - 缺失值: {gpu_data.isnull().sum()}, 范围: {gpu_data.min():.2f} - {gpu_data.max():.2f}")
            
            # 检查有效数据点
            valid_mask = ~(mobile_data.isnull() | gpu_data.isnull())
            valid_count = valid_mask.sum()
            print(f"有效数据点数量: {valid_count}")
            
            if valid_count >= 3:
                print("✓ 数据质量良好，可以进行DEA分析")
                return True
            else:
                print("✗ 有效数据点太少，无法进行DEA分析")
                return False
        else:
            print("✗ 未找到目标列")
            print("请确保数据文件包含mobiledatatraffic和gpu相关的列")
            return False
            
    except FileNotFoundError:
        print("✗ 未找到数据.xlsx文件")
        print("请确保数据文件位于当前目录")
        return False
    except Exception as e:
        print(f"✗ 数据读取错误: {e}")
        return False

def main():
    print("="*50)
    print("数据文件检查工具")
    print("="*50)
    
    success = check_data()
    
    print("\n" + "="*50)
    if success:
        print("数据检查通过！可以运行DEA分析程序。")
        print("请运行: python dea_analysis.py 或 python simple_dea.py")
    else:
        print("数据检查失败！请检查数据文件和格式。")
    print("="*50)

if __name__ == "__main__":
    main()
